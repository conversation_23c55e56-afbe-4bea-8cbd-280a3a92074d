#!/usr/bin/env python3
"""
反激变压器设计主程序
整合所有设计、分析、仿真和测试功能

使用方法:
python main_design_tool.py

功能模块:
1. 变压器参数设计
2. 噪音分析优化
3. 电路仿真验证
4. 综合测试评估
5. 设计报告生成
"""

import sys
import os
import json
from datetime import datetime
import matplotlib.pyplot as plt

# 导入自定义模块
from flyback_transformer_design import FlybackTransformerDesigner, TransformerSpec
from noise_analysis import NoiseAnalyzer
from circuit_simulation import FlybackSimulator
from test_and_validation import TransformerValidator

class TransformerDesignTool:
    """反激变压器设计工具主类"""
    
    def __init__(self):
        self.spec = TransformerSpec()
        self.design_results = {}
        self.simulation_results = {}
        self.noise_analysis = {}
        self.test_results = {}
        
    def print_welcome(self):
        """打印欢迎信息"""
        print("=" * 80)
        print("反激变压器设计工具 v1.0")
        print("=" * 80)
        print("设计要求:")
        print(f"  输入电压: {self.spec.vin_nom}V DC")
        print(f"  输出1: {self.spec.num_9v}路 × {self.spec.vout_9v}V @ {self.spec.iout_9v}A")
        print(f"  输出2: {self.spec.num_18v}路 × {self.spec.vout_18v}V @ {self.spec.iout_18v}A")
        print(f"  总功率: {self.spec.num_9v * self.spec.vout_9v * self.spec.iout_9v + self.spec.num_18v * self.spec.vout_18v * self.spec.iout_18v}W")
        print(f"  设计目标: 最低噪音")
        print("=" * 80)
    
    def run_transformer_design(self):
        """运行变压器设计"""
        print("\n🔧 步骤1: 变压器参数设计")
        print("-" * 50)
        
        designer = FlybackTransformerDesigner(self.spec)
        self.design_results = designer.design_complete_transformer()
        
        print("✓ 变压器设计完成")
        return True
    
    def run_noise_analysis(self):
        """运行噪音分析"""
        print("\n🔊 步骤2: 噪音分析与优化")
        print("-" * 50)
        
        analyzer = NoiseAnalyzer(self.design_results)
        self.noise_analysis = analyzer.generate_noise_optimization_report()
        
        print("✓ 噪音分析完成")
        return True
    
    def run_circuit_simulation(self):
        """运行电路仿真"""
        print("\n⚡ 步骤3: 电路仿真验证")
        print("-" * 50)
        
        simulator = FlybackSimulator(self.design_results)
        self.simulation_results = simulator.run_complete_simulation()
        
        print("✓ 电路仿真完成")
        return True
    
    def run_comprehensive_testing(self):
        """运行综合测试"""
        print("\n🧪 步骤4: 综合测试验证")
        print("-" * 50)
        
        validator = TransformerValidator()
        self.test_results = validator.generate_test_report()
        
        print("✓ 综合测试完成")
        return True
    
    def generate_design_summary(self):
        """生成设计总结"""
        print("\n" + "=" * 80)
        print("🎯 设计结果总结")
        print("=" * 80)
        
        # 核心设计参数
        print("\n📋 核心设计参数:")
        print("-" * 40)
        print(f"磁芯型号: {self.design_results.get('core_type', 'N/A')}")
        print(f"一次侧匝数: {self.design_results.get('np', 'N/A')}匝")
        print(f"9V二次侧匝数: {self.design_results.get('ns_9v', 'N/A')}匝 × 4路")
        print(f"18V二次侧匝数: {self.design_results.get('ns_18v', 'N/A')}匝 × 2路")
        print(f"激磁电感: {self.design_results.get('lm_actual', 0)*1e6:.1f}μH")
        print(f"占空比: {self.design_results.get('d_design', 0):.3f}")
        
        # 导线规格
        print(f"\n🔌 导线规格:")
        print("-" * 40)
        print(f"一次侧: AWG{self.design_results.get('awg_p', 'N/A')}")
        print(f"9V二次侧: AWG{self.design_results.get('awg_s_9v', 'N/A')}")
        print(f"18V二次侧: AWG{self.design_results.get('awg_s_18v', 'N/A')}")
        
        # 性能指标
        print(f"\n📊 性能指标:")
        print("-" * 40)
        print(f"效率: {self.simulation_results.get('efficiency', 0):.1f}%")
        print(f"9V输出纹波: {self.simulation_results.get('ripple_voltages', {}).get('9v', 0)*1000:.1f}mV")
        print(f"18V输出纹波: {self.simulation_results.get('ripple_voltages', {}).get('18v', 0)*1000:.1f}mV")
        print(f"峰值激磁电流: {self.simulation_results.get('peak_magnetizing_current', 0):.2f}A")
        
        # 噪音优化
        print(f"\n🔇 噪音优化:")
        print("-" * 40)
        print(f"推荐开关频率: {self.noise_analysis.get('optimal_frequency', 0)/1000:.0f}kHz")
        print(f"推荐磁芯材料: {self.noise_analysis.get('best_core_material', 'N/A')}")
        
        # 温度分析
        temps = self.simulation_results.get('temperatures', {})
        print(f"\n🌡️ 温度分析:")
        print("-" * 40)
        print(f"MOSFET温度: {temps.get('mosfet', 0):.1f}°C")
        print(f"磁芯温度: {temps.get('core', 0):.1f}°C")
        print(f"二极管温度: {temps.get('diode', 0):.1f}°C")
    
    def save_design_data(self, filename="transformer_design_results.json"):
        """保存设计数据到文件"""
        design_data = {
            'timestamp': datetime.now().isoformat(),
            'specifications': {
                'input_voltage': self.spec.vin_nom,
                'output_9v': {'voltage': self.spec.vout_9v, 'current': self.spec.iout_9v, 'channels': self.spec.num_9v},
                'output_18v': {'voltage': self.spec.vout_18v, 'current': self.spec.iout_18v, 'channels': self.spec.num_18v},
                'switching_frequency': self.spec.frequency
            },
            'design_results': self.design_results,
            'simulation_results': self.simulation_results,
            'noise_analysis': self.noise_analysis
        }
        
        # 转换numpy数组为列表以便JSON序列化
        def convert_numpy(obj):
            if hasattr(obj, 'tolist'):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        design_data = convert_numpy(design_data)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(design_data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 设计数据已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存设计数据失败: {e}")
    
    def generate_manufacturing_guide(self):
        """生成制造指导"""
        print("\n" + "=" * 80)
        print("🏭 制造指导")
        print("=" * 80)
        
        print("\n📦 物料清单 (BOM):")
        print("-" * 50)
        print(f"1. 磁芯: {self.design_results.get('core_type', 'EE25')} × 1套")
        print(f"2. 骨架: {self.design_results.get('core_type', 'EE25')}专用骨架 × 1个")
        print(f"3. 一次侧导线: AWG{self.design_results.get('awg_p', 22)} × 约2米")
        print(f"4. 9V二次侧导线: AWG{self.design_results.get('awg_s_9v', 24)} × 约1米")
        print(f"5. 18V二次侧导线: AWG{self.design_results.get('awg_s_18v', 26)} × 约0.5米")
        print("6. 绝缘纸: 0.1mm厚 × 适量")
        print("7. 绝缘胶带: 高温胶带")
        
        print("\n🔧 绕制步骤:")
        print("-" * 50)
        winding_layout = self.noise_analysis.get('winding_layout', {})
        for i, (layer, description) in enumerate(winding_layout.items(), 1):
            print(f"{i}. {description}")
            if i < len(winding_layout):
                print("   → 加绝缘纸")
        
        print("\n⚠️ 注意事项:")
        print("-" * 50)
        print("1. 绕制时保持导线张力均匀")
        print("2. 层间绝缘必须完整，无破损")
        print("3. 引脚焊接要牢固，避免虚焊")
        print("4. 最终测试前检查绝缘电阻")
        print("5. 变压器应进行浸漆处理")
    
    def run_complete_design(self):
        """运行完整设计流程"""
        try:
            # 打印欢迎信息
            self.print_welcome()
            
            # 执行设计步骤
            self.run_transformer_design()
            self.run_noise_analysis()
            self.run_circuit_simulation()
            self.run_comprehensive_testing()
            
            # 生成结果
            self.generate_design_summary()
            self.generate_manufacturing_guide()
            
            # 保存数据
            self.save_design_data()
            
            print("\n" + "=" * 80)
            print("🎉 设计完成！")
            print("=" * 80)
            print("所有设计文件和图表已生成，请查看当前目录。")
            print("建议下一步：制作样品并进行实际测试验证。")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 设计过程中出现错误: {e}")
            print("请检查输入参数或联系技术支持。")
            return False
    
    def interactive_mode(self):
        """交互模式"""
        while True:
            print("\n" + "=" * 50)
            print("反激变压器设计工具 - 交互模式")
            print("=" * 50)
            print("1. 运行完整设计")
            print("2. 仅运行变压器设计")
            print("3. 仅运行噪音分析")
            print("4. 仅运行电路仿真")
            print("5. 仅运行综合测试")
            print("6. 查看设计总结")
            print("7. 生成制造指导")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-7): ").strip()
            
            if choice == '0':
                print("感谢使用！")
                break
            elif choice == '1':
                self.run_complete_design()
            elif choice == '2':
                self.run_transformer_design()
            elif choice == '3':
                if self.design_results:
                    self.run_noise_analysis()
                else:
                    print("请先运行变压器设计！")
            elif choice == '4':
                if self.design_results:
                    self.run_circuit_simulation()
                else:
                    print("请先运行变压器设计！")
            elif choice == '5':
                self.run_comprehensive_testing()
            elif choice == '6':
                if self.design_results:
                    self.generate_design_summary()
                else:
                    print("请先运行设计！")
            elif choice == '7':
                if self.design_results:
                    self.generate_manufacturing_guide()
                else:
                    print("请先运行设计！")
            else:
                print("无效选择，请重新输入！")

def main():
    """主函数"""
    tool = TransformerDesignTool()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        tool.interactive_mode()
    else:
        tool.run_complete_design()

if __name__ == "__main__":
    main()
