#!/usr/bin/env python3
"""
运行所有测试的主脚本
在conda环境中执行所有设计和测试
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 80)
    print(f" {title}")
    print("=" * 80)

def print_section(title):
    """打印章节标题"""
    print(f"\n🔧 {title}")
    print("-" * 60)

def run_test_module(module_name, description):
    """运行测试模块"""
    print_section(f"运行 {description}")
    
    try:
        # 导入并运行模块
        if module_name == "quick_test":
            from quick_test import quick_design_test
            results = quick_design_test()
            return True, results
        
        elif module_name == "flyback_design":
            from flyback_transformer_design import FlybackTransformerDesigner, TransformerSpec
            spec = TransformerSpec()
            designer = FlybackTransformerDesigner(spec)
            results = designer.design_complete_transformer()
            return True, results
        
        elif module_name == "noise_analysis":
            # 需要先有设计结果
            from flyback_transformer_design import FlybackTransformerDesigner, TransformerSpec
            from noise_analysis import NoiseAnalyzer
            
            spec = TransformerSpec()
            designer = FlybackTransformerDesigner(spec)
            design_results = designer.design_complete_transformer()
            
            analyzer = NoiseAnalyzer(design_results)
            results = analyzer.generate_noise_optimization_report()
            return True, results
        
        elif module_name == "circuit_simulation":
            # 需要先有设计结果
            from flyback_transformer_design import FlybackTransformerDesigner, TransformerSpec
            from circuit_simulation import FlybackSimulator
            
            spec = TransformerSpec()
            designer = FlybackTransformerDesigner(spec)
            design_results = designer.design_complete_transformer()
            
            # 禁用matplotlib显示
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            
            simulator = FlybackSimulator(design_results)
            results = simulator.run_complete_simulation()
            return True, results
        
        elif module_name == "comprehensive_test":
            from test_and_validation import TransformerValidator
            
            # 禁用matplotlib显示
            import matplotlib
            matplotlib.use('Agg')
            
            validator = TransformerValidator()
            results = validator.generate_test_report()
            return True, results
        
        else:
            print(f"❌ 未知的测试模块: {module_name}")
            return False, None
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需的依赖包")
        return False, None
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False, None

def check_environment():
    """检查运行环境"""
    print_section("环境检查")
    
    # 检查Python版本
    python_version = sys.version
    print(f"✓ Python版本: {python_version}")
    
    # 检查必要的包
    required_packages = ['numpy', 'matplotlib', 'scipy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n警告: 缺少以下包: {', '.join(missing_packages)}")
        print("某些功能可能无法正常工作")
        return False
    
    return True

def save_test_results(all_results):
    """保存测试结果"""
    print_section("保存测试结果")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"test_results_{timestamp}.txt"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("反激变压器设计测试结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for test_name, (success, results) in all_results.items():
                f.write(f"\n{test_name}:\n")
                f.write("-" * 30 + "\n")
                if success:
                    f.write("状态: 成功\n")
                    if results:
                        f.write(f"结果: {str(results)[:500]}...\n")  # 限制长度
                else:
                    f.write("状态: 失败\n")
        
        print(f"✓ 测试结果已保存到: {filename}")
        return filename
    
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")
        return None

def main():
    """主函数"""
    print_header("反激变压器设计工具 - 完整测试")
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 环境检查
    env_ok = check_environment()
    
    # 定义测试项目
    test_modules = [
        ("quick_test", "快速设计测试"),
        ("flyback_design", "变压器参数设计"),
        ("noise_analysis", "噪音分析优化"),
        ("circuit_simulation", "电路仿真验证"),
        ("comprehensive_test", "综合测试评估")
    ]
    
    # 运行所有测试
    all_results = {}
    success_count = 0
    
    for module_name, description in test_modules:
        success, results = run_test_module(module_name, description)
        all_results[description] = (success, results)
        
        if success:
            success_count += 1
            print(f"✓ {description} - 成功")
        else:
            print(f"❌ {description} - 失败")
    
    # 测试总结
    print_header("测试总结")
    total_tests = len(test_modules)
    print(f"总测试项目: {total_tests}")
    print(f"成功项目: {success_count}")
    print(f"失败项目: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    # 保存结果
    result_file = save_test_results(all_results)
    
    # 最终状态
    if success_count == total_tests:
        print("\n🎉 所有测试通过！设计工具运行正常。")
    elif success_count > 0:
        print(f"\n⚠️ 部分测试通过 ({success_count}/{total_tests})，请检查失败项目。")
    else:
        print("\n❌ 所有测试失败，请检查环境配置。")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success_count == total_tests

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期的错误: {e}")
        sys.exit(1)
