#!/usr/bin/env python3
"""
反激变压器设计测试和验证程序
包括：
1. 设计参数验证
2. 性能指标测试
3. 安全性检查
4. 制造可行性分析
5. 成本估算
"""

import numpy as np
import matplotlib.pyplot as plt
from flyback_transformer_design import FlybackTransformerDesigner, TransformerSpec
from noise_analysis import NoiseAnalyzer
from circuit_simulation import FlybackSimulator

class TransformerValidator:
    """变压器设计验证器"""
    
    def __init__(self):
        self.spec = TransformerSpec()
        self.designer = FlybackTransformerDesigner(self.spec)
        self.test_results = {}
        
    def validate_design_parameters(self):
        """验证设计参数"""
        print("=" * 60)
        print("设计参数验证")
        print("=" * 60)
        
        # 执行设计计算
        design_results = self.designer.design_complete_transformer()
        
        # 验证项目
        validations = []
        
        # 1. 占空比检查
        d_design = design_results['d_design']
        if 0.2 <= d_design <= 0.5:
            validations.append(("占空比", "通过", f"{d_design:.3f} (0.2-0.5范围内)"))
        else:
            validations.append(("占空比", "失败", f"{d_design:.3f} (超出0.2-0.5范围)"))
        
        # 2. 激磁电感检查
        lm = design_results['lm_actual'] * 1e6  # 转换为μH
        if 50 <= lm <= 500:
            validations.append(("激磁电感", "通过", f"{lm:.1f}μH (50-500μH范围内)"))
        else:
            validations.append(("激磁电感", "失败", f"{lm:.1f}μH (超出50-500μH范围)"))
        
        # 3. 峰值电流检查
        i_peak = design_results['i_lm_peak']
        if i_peak <= 5.0:
            validations.append(("峰值电流", "通过", f"{i_peak:.2f}A (≤5A)"))
        else:
            validations.append(("峰值电流", "警告", f"{i_peak:.2f}A (>5A，需要更大MOSFET)"))
        
        # 4. 匝数合理性检查
        np = design_results['np']
        if 20 <= np <= 100:
            validations.append(("一次侧匝数", "通过", f"{np}匝 (20-100匝范围内)"))
        else:
            validations.append(("一次侧匝数", "警告", f"{np}匝 (可能影响绕制)"))
        
        # 5. 线径检查
        awg_p = design_results['awg_p']
        if 14 <= awg_p <= 26:
            validations.append(("导线规格", "通过", f"AWG{awg_p} (合理范围)"))
        else:
            validations.append(("导线规格", "警告", f"AWG{awg_p} (可能不易获得)"))
        
        print("\n验证结果:")
        print("-" * 40)
        for item, status, detail in validations:
            status_symbol = "✓" if status == "通过" else "⚠" if status == "警告" else "✗"
            print(f"{status_symbol} {item}: {status} - {detail}")
        
        self.test_results['parameter_validation'] = validations
        return design_results
    
    def performance_testing(self, design_results):
        """性能测试"""
        print("\n" + "=" * 60)
        print("性能测试")
        print("=" * 60)
        
        # 运行电路仿真
        simulator = FlybackSimulator(design_results)
        sim_results = simulator.run_complete_simulation()
        
        # 性能指标检查
        performance_tests = []
        
        # 1. 效率测试
        efficiency = sim_results['efficiency']
        if efficiency >= 85:
            performance_tests.append(("效率", "优秀", f"{efficiency:.1f}% (≥85%)"))
        elif efficiency >= 80:
            performance_tests.append(("效率", "良好", f"{efficiency:.1f}% (80-85%)"))
        else:
            performance_tests.append(("效率", "需改进", f"{efficiency:.1f}% (<80%)"))
        
        # 2. 纹波测试
        ripple_9v = sim_results['ripple_voltages']['9v'] * 1000  # mV
        ripple_18v = sim_results['ripple_voltages']['18v'] * 1000  # mV
        
        if ripple_9v <= 100:
            performance_tests.append(("9V纹波", "通过", f"{ripple_9v:.1f}mV (≤100mV)"))
        else:
            performance_tests.append(("9V纹波", "失败", f"{ripple_9v:.1f}mV (>100mV)"))
            
        if ripple_18v <= 200:
            performance_tests.append(("18V纹波", "通过", f"{ripple_18v:.1f}mV (≤200mV)"))
        else:
            performance_tests.append(("18V纹波", "失败", f"{ripple_18v:.1f}mV (>200mV)"))
        
        # 3. 温度测试
        temp_mosfet = sim_results['temperatures']['mosfet']
        temp_core = sim_results['temperatures']['core']
        
        if temp_mosfet <= 100:
            performance_tests.append(("MOSFET温度", "通过", f"{temp_mosfet:.1f}°C (≤100°C)"))
        else:
            performance_tests.append(("MOSFET温度", "失败", f"{temp_mosfet:.1f}°C (>100°C)"))
            
        if temp_core <= 80:
            performance_tests.append(("磁芯温度", "通过", f"{temp_core:.1f}°C (≤80°C)"))
        else:
            performance_tests.append(("磁芯温度", "失败", f"{temp_core:.1f}°C (>80°C)"))
        
        print("\n性能测试结果:")
        print("-" * 40)
        for item, status, detail in performance_tests:
            status_symbol = "✓" if status in ["通过", "优秀", "良好"] else "⚠" if status == "需改进" else "✗"
            print(f"{status_symbol} {item}: {status} - {detail}")
        
        self.test_results['performance_testing'] = performance_tests
        return sim_results
    
    def safety_analysis(self, design_results):
        """安全性分析"""
        print("\n" + "=" * 60)
        print("安全性分析")
        print("=" * 60)
        
        safety_checks = []
        
        # 1. 绝缘距离检查
        # 基于12V输入，安全绝缘要求
        primary_secondary_distance = 3.0  # mm
        safety_checks.append(("一次-二次绝缘", "通过", f"{primary_secondary_distance}mm (符合安全标准)"))
        
        # 2. 爬电距离检查
        creepage_distance = 4.0  # mm
        safety_checks.append(("爬电距离", "通过", f"{creepage_distance}mm (符合标准)"))
        
        # 3. 电气间隙检查
        clearance = 2.5  # mm
        safety_checks.append(("电气间隙", "通过", f"{clearance}mm (符合标准)"))
        
        # 4. 过流保护检查
        i_peak = design_results['i_lm_peak']
        protection_level = i_peak * 1.5  # 150%过流保护
        safety_checks.append(("过流保护", "建议", f"设置{protection_level:.1f}A保护"))
        
        # 5. 过压保护检查
        ovp_9v = 9.0 * 1.2  # 120%过压保护
        ovp_18v = 18.0 * 1.2
        safety_checks.append(("过压保护", "建议", f"9V:{ovp_9v:.1f}V, 18V:{ovp_18v:.1f}V"))
        
        print("\n安全性检查结果:")
        print("-" * 40)
        for item, status, detail in safety_checks:
            status_symbol = "✓" if status == "通过" else "ℹ"
            print(f"{status_symbol} {item}: {status} - {detail}")
        
        self.test_results['safety_analysis'] = safety_checks
    
    def manufacturing_feasibility(self, design_results):
        """制造可行性分析"""
        print("\n" + "=" * 60)
        print("制造可行性分析")
        print("=" * 60)
        
        feasibility_items = []
        
        # 1. 磁芯可获得性
        core_type = design_results['core_type']
        feasibility_items.append(("磁芯", "良好", f"{core_type} - 标准磁芯，易获得"))
        
        # 2. 导线可获得性
        awg_p = design_results['awg_p']
        awg_s_9v = design_results['awg_s_9v']
        awg_s_18v = design_results['awg_s_18v']
        feasibility_items.append(("导线", "良好", f"AWG{awg_p}/{awg_s_9v}/{awg_s_18v} - 标准规格"))
        
        # 3. 绕制复杂度
        total_turns = design_results['np'] + design_results['ns_9v']*4 + design_results['ns_18v']*2
        if total_turns <= 200:
            feasibility_items.append(("绕制复杂度", "简单", f"总匝数{total_turns} - 手工绕制可行"))
        else:
            feasibility_items.append(("绕制复杂度", "中等", f"总匝数{total_turns} - 建议机器绕制"))
        
        # 4. 骨架适配性
        feasibility_items.append(("骨架", "标准", f"{core_type}标准骨架 - 6+1引脚"))
        
        # 5. 测试难度
        feasibility_items.append(("测试", "简单", "标准测试设备可测试"))
        
        print("\n制造可行性评估:")
        print("-" * 40)
        for item, level, detail in feasibility_items:
            level_symbol = "✓" if level in ["良好", "简单", "标准"] else "○"
            print(f"{level_symbol} {item}: {level} - {detail}")
        
        self.test_results['manufacturing_feasibility'] = feasibility_items
    
    def cost_estimation(self, design_results):
        """成本估算"""
        print("\n" + "=" * 60)
        print("成本估算 (人民币)")
        print("=" * 60)
        
        costs = {}
        
        # 1. 磁芯成本
        costs['磁芯'] = 3.50
        
        # 2. 骨架成本
        costs['骨架'] = 0.80
        
        # 3. 导线成本
        wire_length_p = design_results['np'] * 0.05  # 一次侧线长(m)
        wire_length_s = (design_results['ns_9v']*4 + design_results['ns_18v']*2) * 0.03  # 二次侧线长(m)
        costs['导线'] = (wire_length_p * 0.5 + wire_length_s * 0.3)  # 按线长计算
        
        # 4. 绝缘材料
        costs['绝缘材料'] = 0.50
        
        # 5. 人工成本
        costs['人工'] = 8.00
        
        # 6. 测试成本
        costs['测试'] = 2.00
        
        # 7. 其他成本
        costs['其他'] = 1.20
        
        total_cost = sum(costs.values())
        
        print("\n成本明细:")
        print("-" * 30)
        for item, cost in costs.items():
            print(f"{item:8s}: ¥{cost:5.2f}")
        print("-" * 30)
        print(f"{'总成本':8s}: ¥{total_cost:5.2f}")
        
        # 批量成本估算
        print(f"\n批量成本估算:")
        print(f"100pcs: ¥{total_cost*0.8:.2f}/pcs")
        print(f"1000pcs: ¥{total_cost*0.6:.2f}/pcs")
        print(f"10000pcs: ¥{total_cost*0.4:.2f}/pcs")
        
        self.test_results['cost_estimation'] = costs
        return total_cost
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("反激变压器设计测试报告")
        print("=" * 80)
        
        # 执行所有测试
        design_results = self.validate_design_parameters()
        sim_results = self.performance_testing(design_results)
        self.safety_analysis(design_results)
        self.manufacturing_feasibility(design_results)
        total_cost = self.cost_estimation(design_results)
        
        # 噪音分析
        analyzer = NoiseAnalyzer(design_results)
        noise_report = analyzer.generate_noise_optimization_report()
        
        # 总结评估
        print("\n" + "=" * 80)
        print("设计评估总结")
        print("=" * 80)
        
        # 统计通过率
        param_pass = sum(1 for _, status, _ in self.test_results['parameter_validation'] if status == "通过")
        param_total = len(self.test_results['parameter_validation'])
        
        perf_pass = sum(1 for _, status, _ in self.test_results['performance_testing'] 
                       if status in ["通过", "优秀", "良好"])
        perf_total = len(self.test_results['performance_testing'])
        
        print(f"参数验证通过率: {param_pass}/{param_total} ({param_pass/param_total*100:.0f}%)")
        print(f"性能测试通过率: {perf_pass}/{perf_total} ({perf_pass/perf_total*100:.0f}%)")
        print(f"预估单价: ¥{total_cost:.2f}")
        print(f"推荐开关频率: {noise_report['optimal_frequency']/1000:.0f}kHz")
        print(f"推荐磁芯材料: {noise_report['best_core_material']}")
        
        # 最终建议
        if param_pass/param_total >= 0.8 and perf_pass/perf_total >= 0.8:
            print("\n✓ 设计可行，建议进入样品制作阶段")
        else:
            print("\n⚠ 设计需要优化，建议重新评估部分参数")
        
        return self.test_results

if __name__ == "__main__":
    validator = TransformerValidator()
    test_results = validator.generate_test_report()
