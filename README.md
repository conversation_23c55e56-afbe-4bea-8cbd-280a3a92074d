# 反激变压器设计工具

一个专业的反激变压器设计、分析和验证工具集，专门针对低噪音设计优化。

## 设计要求

- **输入电压**: 12V DC
- **输出规格**: 
  - 4路 9V @ 4.5W (隔离输出)
  - 2路 18V @ 4.5W (隔离输出)
- **总功率**: 27W
- **设计目标**: 最低噪音

## 功能特性

### 🔧 变压器设计
- 自动计算激磁电感、匝数比、导线规格
- 支持多路隔离输出设计
- 优化占空比和开关频率选择

### 🔊 噪音分析
- 开关频率对噪音影响分析
- 磁芯材料噪音特性比较
- EMI滤波器设计
- 绕组布局优化

### ⚡ 电路仿真
- 时域波形仿真
- 效率计算和损耗分析
- 热分析和温升计算
- 输出纹波分析

### 🧪 综合测试
- 设计参数验证
- 性能指标测试
- 安全性检查
- 制造可行性分析
- 成本估算

## 安装和使用

### 环境要求
- Python 3.7+
- numpy, matplotlib, scipy

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行设计工具

#### 方式1: 完整设计流程
```bash
python main_design_tool.py
```

#### 方式2: 交互模式
```bash
python main_design_tool.py --interactive
```

#### 方式3: 单独运行模块
```bash
# 仅运行变压器设计
python flyback_transformer_design.py

# 仅运行噪音分析
python noise_analysis.py

# 仅运行电路仿真
python circuit_simulation.py

# 仅运行综合测试
python test_and_validation.py
```

## 输出文件

运行后会生成以下文件：
- `transformer_design_results.json` - 完整设计数据
- `simulation_results.png` - 仿真波形图
- `emi_filter_response.png` - EMI滤波器响应图

## 设计结果示例

### 核心参数
- **磁芯**: EE25
- **一次侧**: 45匝, AWG22
- **9V二次侧**: 8匝 × 4路, AWG24
- **18V二次侧**: 15匝 × 2路, AWG26
- **激磁电感**: 100μH
- **开关频率**: 100kHz

### 性能指标
- **效率**: ≥85%
- **9V纹波**: <100mV
- **18V纹波**: <200mV
- **温升**: <80°C

## 文件结构

```
├── main_design_tool.py          # 主程序
├── flyback_transformer_design.py # 变压器设计模块
├── noise_analysis.py            # 噪音分析模块
├── circuit_simulation.py        # 电路仿真模块
├── test_and_validation.py       # 测试验证模块
├── requirements.txt             # 依赖包列表
└── README.md                    # 说明文档
```

## 设计流程

1. **参数设计** - 计算激磁电感、匝数、导线规格
2. **噪音优化** - 分析并优化开关频率、磁芯材料
3. **电路仿真** - 验证性能指标和热特性
4. **综合测试** - 全面验证设计可行性
5. **制造指导** - 生成BOM和绕制指导

## 噪音优化策略

### 开关频率选择
- 避开人耳敏感频段(1-4kHz)的谐波
- 推荐100kHz开关频率

### 磁芯材料
- 推荐N27材料(低损耗、低磁致伸缩)
- 使用气隙分布技术

### 绕组布局
- 三明治结构减少漏感
- 优化层间绝缘

### EMI滤波
- 差模滤波器: 100μH + 0.1μF
- 共模滤波器: 1mH + 2.2nF

## 制造指导

### 物料清单
1. EE25磁芯套装
2. EE25专用骨架
3. AWG22/24/26导线
4. 绝缘材料

### 绕制步骤
1. 一次侧(22匝) → 绝缘纸
2. 9V二次侧(8匝×4) → 绝缘纸  
3. 一次侧(23匝) → 绝缘纸
4. 18V二次侧(15匝×2)

### 测试验证
- 绝缘电阻测试
- 匝比测试
- 电感测试
- 耐压测试

## 注意事项

1. **安全第一** - 严格按照绝缘要求制造
2. **质量控制** - 每个变压器都要测试
3. **热管理** - 确保充分散热
4. **EMC设计** - 添加必要的滤波器

## 技术支持

如有问题请检查：
1. Python环境和依赖包
2. 输入参数是否合理
3. 设计结果是否通过验证

## 版本历史

- v1.0 - 初始版本，包含完整设计流程

---

**注意**: 本工具提供设计参考，实际制造前请进行样品验证。
