#!/usr/bin/env python3
"""
反激变压器电路仿真和验证
包括：
1. 时域仿真
2. 稳态分析
3. 瞬态响应
4. 效率计算
5. 热分析
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
from scipy import signal
import math

class FlybackSimulator:
    """反激变压器仿真器"""
    
    def __init__(self, transformer_params):
        self.params = transformer_params
        self.setup_circuit_parameters()
        
    def setup_circuit_parameters(self):
        """设置电路参数"""
        # 从变压器设计结果获取参数
        self.Lm = self.params.get('lm_actual', 100e-6)  # 激磁电感
        self.np = self.params.get('np', 45)  # 一次侧匝数
        self.ns_9v = self.params.get('ns_9v', 8)  # 9V二次侧匝数
        self.ns_18v = self.params.get('ns_18v', 15)  # 18V二次侧匝数
        
        # 电路参数
        self.Vin = 12.0  # 输入电压
        self.fsw = 100e3  # 开关频率
        self.Tsw = 1/self.fsw  # 开关周期
        self.D = 0.4  # 占空比
        
        # 负载参数
        self.Rload_9v = 9.0 / 0.5  # 9V负载电阻
        self.Rload_18v = 18.0 / 0.25  # 18V负载电阻
        
        # 寄生参数
        self.Rds_on = 0.1  # MOSFET导通电阻
        self.Vf_diode = 0.7  # 二极管压降
        self.Rd_diode = 0.05  # 二极管电阻
        
        # 输出滤波电容
        self.Co_9v = 220e-6  # 9V输出电容
        self.Co_18v = 100e-6  # 18V输出电容
        self.ESR_9v = 0.05  # 9V电容ESR
        self.ESR_18v = 0.08  # 18V电容ESR
        
    def simulate_magnetizing_current(self, t_sim=None):
        """仿真激磁电流波形"""
        if t_sim is None:
            t_sim = np.linspace(0, 3*self.Tsw, 1000)
        
        iLm = np.zeros_like(t_sim)
        
        for i, t in enumerate(t_sim):
            t_cycle = t % self.Tsw
            
            if t_cycle < self.D * self.Tsw:
                # 开关导通期间，电流线性上升
                iLm[i] = (self.Vin / self.Lm) * t_cycle
            else:
                # 开关关断期间，电流线性下降
                t_off = t_cycle - self.D * self.Tsw
                iLm_peak = (self.Vin / self.Lm) * self.D * self.Tsw
                
                # 计算反射电压
                Vreflected_9v = (self.ns_9v / self.np) * (9.0 + self.Vf_diode)
                Vreflected_18v = (self.ns_18v / self.np) * (18.0 + self.Vf_diode)
                Vreflected = min(Vreflected_9v, Vreflected_18v)  # 取较小值
                
                iLm[i] = iLm_peak - (Vreflected / self.Lm) * t_off
                if iLm[i] < 0:
                    iLm[i] = 0  # 电流不能为负
        
        return t_sim, iLm
    
    def simulate_output_voltages(self, t_sim=None):
        """仿真输出电压波形"""
        if t_sim is None:
            t_sim = np.linspace(0, 10*self.Tsw, 2000)
        
        # 简化的输出电压仿真
        vo_9v = np.zeros_like(t_sim)
        vo_18v = np.zeros_like(t_sim)
        
        # 理想输出电压
        Vo_ideal_9v = 9.0
        Vo_ideal_18v = 18.0
        
        # 纹波计算
        Iout_9v = 0.5  # 输出电流
        Iout_18v = 0.25
        
        # 纹波幅度 = I * ESR + I * dt / C
        ripple_9v = Iout_9v * self.ESR_9v + (Iout_9v * self.D * self.Tsw) / (2 * self.Co_9v)
        ripple_18v = Iout_18v * self.ESR_18v + (Iout_18v * self.D * self.Tsw) / (2 * self.Co_18v)
        
        for i, t in enumerate(t_sim):
            # 添加开关纹波
            ripple_phase = 2 * np.pi * self.fsw * t
            vo_9v[i] = Vo_ideal_9v + ripple_9v * np.sin(ripple_phase)
            vo_18v[i] = Vo_ideal_18v + ripple_18v * np.sin(ripple_phase)
        
        return t_sim, vo_9v, vo_18v, ripple_9v, ripple_18v
    
    def calculate_efficiency(self):
        """计算效率"""
        print("\n效率计算:")
        print("-" * 20)
        
        # 输出功率
        Po_9v = 9.0 * 0.5 * 4  # 4路9V输出
        Po_18v = 18.0 * 0.25 * 2  # 2路18V输出
        Po_total = Po_9v + Po_18v
        
        # 损耗计算
        # 1. MOSFET导通损耗
        Irms_primary = 1.2  # 估算一次侧RMS电流
        P_mosfet_cond = Irms_primary**2 * self.Rds_on
        
        # 2. MOSFET开关损耗 (简化计算)
        P_mosfet_sw = 0.5 * 12 * 2 * 50e-9 * self.fsw  # Vin * Ipeak * (tr+tf) * fsw
        
        # 3. 磁芯损耗 (经验公式)
        Bmax = 0.2  # 最大磁感应强度 (T)
        Ve = 3.6e-6  # EE25磁芯体积 (m³)
        P_core = 100 * (self.fsw/1000)**1.3 * Bmax**2.4 * Ve * 1000  # mW
        P_core = P_core / 1000  # 转换为W
        
        # 4. 二极管损耗
        P_diode_9v = self.Vf_diode * 0.5 * 4  # 4路9V
        P_diode_18v = self.Vf_diode * 0.25 * 2  # 2路18V
        P_diode_total = P_diode_9v + P_diode_18v
        
        # 5. 绕组损耗 (铜损)
        # 简化计算，假设总铜损为2W
        P_copper = 2.0
        
        # 总损耗
        P_loss_total = P_mosfet_cond + P_mosfet_sw + P_core + P_diode_total + P_copper
        
        # 输入功率
        Pin = Po_total + P_loss_total
        
        # 效率
        efficiency = Po_total / Pin * 100
        
        print(f"输出功率: {Po_total:.1f}W")
        print(f"损耗分析:")
        print(f"  MOSFET导通损耗: {P_mosfet_cond:.2f}W")
        print(f"  MOSFET开关损耗: {P_mosfet_sw:.2f}W")
        print(f"  磁芯损耗: {P_core:.2f}W")
        print(f"  二极管损耗: {P_diode_total:.2f}W")
        print(f"  铜损: {P_copper:.2f}W")
        print(f"总损耗: {P_loss_total:.2f}W")
        print(f"输入功率: {Pin:.1f}W")
        print(f"效率: {efficiency:.1f}%")
        
        return efficiency, P_loss_total
    
    def thermal_analysis(self, P_loss):
        """热分析"""
        print("\n热分析:")
        print("-" * 20)
        
        # 热阻参数 (°C/W)
        Rth_ja_mosfet = 50  # MOSFET结到环境热阻
        Rth_ja_core = 30    # 磁芯到环境热阻
        Rth_ja_diode = 80   # 二极管结到环境热阻
        
        Ta = 25  # 环境温度 (°C)
        
        # 温升计算
        P_mosfet = 0.5  # MOSFET总损耗
        P_core = 0.3    # 磁芯损耗
        P_diode = 0.4   # 二极管损耗
        
        T_mosfet = Ta + P_mosfet * Rth_ja_mosfet
        T_core = Ta + P_core * Rth_ja_core
        T_diode = Ta + P_diode * Rth_ja_diode
        
        print(f"环境温度: {Ta}°C")
        print(f"MOSFET温度: {T_mosfet:.1f}°C")
        print(f"磁芯温度: {T_core:.1f}°C")
        print(f"二极管温度: {T_diode:.1f}°C")
        
        # 温度检查
        if T_mosfet > 100:
            print("警告: MOSFET温度过高!")
        if T_core > 80:
            print("警告: 磁芯温度过高!")
        if T_diode > 125:
            print("警告: 二极管温度过高!")
        
        return T_mosfet, T_core, T_diode
    
    def plot_simulation_results(self):
        """绘制仿真结果"""
        # 激磁电流仿真
        t_iLm, iLm = self.simulate_magnetizing_current()
        
        # 输出电压仿真
        t_vo, vo_9v, vo_18v, ripple_9v, ripple_18v = self.simulate_output_voltages()
        
        plt.figure(figsize=(15, 10))
        
        # 激磁电流波形
        plt.subplot(3, 1, 1)
        plt.plot(t_iLm*1e6, iLm, 'b-', linewidth=2)
        plt.grid(True)
        plt.xlabel('时间 (μs)')
        plt.ylabel('激磁电流 (A)')
        plt.title('激磁电流波形')
        
        # 9V输出电压波形
        plt.subplot(3, 1, 2)
        plt.plot(t_vo*1e6, vo_9v, 'r-', linewidth=2)
        plt.grid(True)
        plt.xlabel('时间 (μs)')
        plt.ylabel('9V输出电压 (V)')
        plt.title(f'9V输出电压波形 (纹波: {ripple_9v*1000:.1f}mV)')
        plt.ylim([8.5, 9.5])
        
        # 18V输出电压波形
        plt.subplot(3, 1, 3)
        plt.plot(t_vo*1e6, vo_18v, 'g-', linewidth=2)
        plt.grid(True)
        plt.xlabel('时间 (μs)')
        plt.ylabel('18V输出电压 (V)')
        plt.title(f'18V输出电压波形 (纹波: {ripple_18v*1000:.1f}mV)')
        plt.ylim([17.5, 18.5])
        
        plt.tight_layout()
        plt.savefig('simulation_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return {
            'ripple_9v': ripple_9v,
            'ripple_18v': ripple_18v,
            'iLm_peak': np.max(iLm)
        }
    
    def run_complete_simulation(self):
        """运行完整仿真"""
        print("=" * 50)
        print("反激变压器电路仿真")
        print("=" * 50)
        
        # 效率计算
        efficiency, P_loss = self.calculate_efficiency()
        
        # 热分析
        T_mosfet, T_core, T_diode = self.thermal_analysis(P_loss)
        
        # 波形仿真
        sim_results = self.plot_simulation_results()
        
        return {
            'efficiency': efficiency,
            'power_loss': P_loss,
            'temperatures': {
                'mosfet': T_mosfet,
                'core': T_core,
                'diode': T_diode
            },
            'ripple_voltages': {
                '9v': sim_results['ripple_9v'],
                '18v': sim_results['ripple_18v']
            },
            'peak_magnetizing_current': sim_results['iLm_peak']
        }

if __name__ == "__main__":
    # 使用示例参数
    params = {
        'lm_actual': 100e-6,
        'np': 45,
        'ns_9v': 8,
        'ns_18v': 15
    }
    
    simulator = FlybackSimulator(params)
    results = simulator.run_complete_simulation()
    
    print("\n" + "=" * 50)
    print("仿真结果总结")
    print("=" * 50)
    print(f"效率: {results['efficiency']:.1f}%")
    print(f"总损耗: {results['power_loss']:.2f}W")
    print(f"9V输出纹波: {results['ripple_voltages']['9v']*1000:.1f}mV")
    print(f"18V输出纹波: {results['ripple_voltages']['18v']*1000:.1f}mV")
    print(f"峰值激磁电流: {results['peak_magnetizing_current']:.2f}A")
