#!/usr/bin/env python3
"""
开关电源噪音分析和优化工具
主要分析：
1. 开关频率选择对噪音的影响
2. 磁芯材料和结构对噪音的影响
3. 绕组结构对噪音的影响
4. EMI滤波器设计
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import math

class NoiseAnalyzer:
    """噪音分析器"""
    
    def __init__(self, transformer_results):
        self.results = transformer_results
        self.frequencies = np.logspace(3, 8, 1000)  # 1kHz to 100MHz
        
    def analyze_switching_frequency_impact(self):
        """分析开关频率对噪音的影响"""
        print("\n开关频率噪音分析:")
        print("-" * 30)
        
        # 测试不同开关频率
        test_frequencies = [50e3, 100e3, 150e3, 200e3, 300e3]  # 50kHz to 300kHz
        
        noise_levels = []
        for f_sw in test_frequencies:
            # 计算基波和谐波噪音
            harmonics = np.arange(1, 21)  # 前20次谐波
            harmonic_freqs = harmonics * f_sw
            
            # 谐波幅度衰减 (1/n规律)
            harmonic_amplitudes = 1.0 / harmonics
            
            # 计算在人耳敏感频段(1-4kHz)的噪音
            audible_noise = 0
            for i, freq in enumerate(harmonic_freqs):
                if 1000 <= freq <= 4000:  # 人耳敏感频段
                    audible_noise += harmonic_amplitudes[i]
            
            noise_levels.append(audible_noise)
            print(f"  {f_sw/1000:.0f}kHz: 可听噪音指数 {audible_noise:.3f}")
        
        # 找到最优频率
        min_noise_idx = np.argmin(noise_levels)
        optimal_freq = test_frequencies[min_noise_idx]
        
        print(f"\n推荐开关频率: {optimal_freq/1000:.0f}kHz (最低可听噪音)")
        
        return optimal_freq, test_frequencies, noise_levels
    
    def analyze_core_material_impact(self):
        """分析磁芯材料对噪音的影响"""
        print("\n磁芯材料噪音分析:")
        print("-" * 30)
        
        # 不同磁芯材料的特性
        core_materials = {
            'N87': {'loss_factor': 1.0, 'magnetostriction': 1.0, 'cost': 1.0},
            'N97': {'loss_factor': 0.7, 'magnetostriction': 0.8, 'cost': 1.2},
            'N49': {'loss_factor': 1.2, 'magnetostriction': 1.3, 'cost': 0.8},
            'N27': {'loss_factor': 0.6, 'magnetostriction': 0.6, 'cost': 1.5}
        }
        
        print("材料\t损耗系数\t磁致伸缩\t成本系数\t噪音指数")
        print("-" * 50)
        
        best_material = None
        min_noise_index = float('inf')
        
        for material, props in core_materials.items():
            # 噪音指数 = 损耗系数 × 磁致伸缩系数
            noise_index = props['loss_factor'] * props['magnetostriction']
            
            print(f"{material}\t{props['loss_factor']:.1f}\t\t{props['magnetostriction']:.1f}\t\t{props['cost']:.1f}\t\t{noise_index:.2f}")
            
            if noise_index < min_noise_index:
                min_noise_index = noise_index
                best_material = material
        
        print(f"\n推荐磁芯材料: {best_material} (最低噪音指数: {min_noise_index:.2f})")
        
        return best_material
    
    def design_emi_filter(self):
        """设计EMI滤波器"""
        print("\nEMI滤波器设计:")
        print("-" * 30)
        
        f_sw = 100e3  # 开关频率
        
        # 差模滤波器设计
        # L-C低通滤波器，截止频率为开关频率的1/10
        f_cutoff_dm = f_sw / 10
        
        # 选择标准电感值
        L_dm = 100e-6  # 100μH
        C_dm = 1 / (4 * math.pi**2 * f_cutoff_dm**2 * L_dm)
        C_dm_std = 0.1e-6  # 选择标准值 0.1μF
        
        # 重新计算实际截止频率
        f_cutoff_dm_actual = 1 / (2 * math.pi * math.sqrt(L_dm * C_dm_std))
        
        print(f"差模滤波器:")
        print(f"  电感: {L_dm*1e6:.0f}μH")
        print(f"  电容: {C_dm_std*1e6:.1f}μF")
        print(f"  截止频率: {f_cutoff_dm_actual/1000:.1f}kHz")
        
        # 共模滤波器设计
        L_cm = 1e-3  # 1mH 共模电感
        C_cm = 2.2e-9  # 2.2nF Y电容
        
        f_cutoff_cm = 1 / (2 * math.pi * math.sqrt(L_cm * C_cm))
        
        print(f"\n共模滤波器:")
        print(f"  共模电感: {L_cm*1e3:.0f}mH")
        print(f"  Y电容: {C_cm*1e9:.1f}nF")
        print(f"  截止频率: {f_cutoff_cm/1000:.1f}kHz")
        
        return {
            'L_dm': L_dm,
            'C_dm': C_dm_std,
            'f_cutoff_dm': f_cutoff_dm_actual,
            'L_cm': L_cm,
            'C_cm': C_cm,
            'f_cutoff_cm': f_cutoff_cm
        }
    
    def calculate_winding_layout_for_low_noise(self):
        """计算低噪音绕组布局"""
        print("\n低噪音绕组布局设计:")
        print("-" * 30)
        
        # 绕组策略：三明治结构减少漏感和噪音
        # 布局：Primary(50%) - Secondary_9V - Primary(50%) - Secondary_18V
        
        np = self.results['np']
        ns_9v = self.results['ns_9v']
        ns_18v = self.results['ns_18v']
        
        # 分层绕组
        layers = {
            'Layer_1': f'Primary ({np//2}匝)',
            'Layer_2': f'9V Secondary ({ns_9v}匝) × 4路',
            'Layer_3': f'Primary ({np - np//2}匝)',
            'Layer_4': f'18V Secondary ({ns_18v}匝) × 2路'
        }
        
        print("推荐绕组层次结构:")
        for layer, description in layers.items():
            print(f"  {layer}: {description}")
        
        # 绝缘要求
        insulation = {
            'Primary-Secondary': '3mm (安全绝缘)',
            'Secondary-Secondary': '1mm (功能绝缘)',
            'Layer-Layer': '0.1mm (层间绝缘纸)'
        }
        
        print("\n绝缘要求:")
        for location, requirement in insulation.items():
            print(f"  {location}: {requirement}")
        
        return layers, insulation
    
    def generate_noise_optimization_report(self):
        """生成噪音优化报告"""
        print("\n" + "=" * 60)
        print("噪音优化设计报告")
        print("=" * 60)
        
        # 开关频率优化
        optimal_freq, _, _ = self.analyze_switching_frequency_impact()
        
        # 磁芯材料优化
        best_material = self.analyze_core_material_impact()
        
        # EMI滤波器设计
        emi_filter = self.design_emi_filter()
        
        # 绕组布局优化
        layers, insulation = self.calculate_winding_layout_for_low_noise()
        
        # 其他噪音抑制措施
        print("\n其他噪音抑制措施:")
        print("-" * 30)
        measures = [
            "1. 使用气隙分布技术减少磁致伸缩噪音",
            "2. 采用软开关技术(准谐振)减少开关噪音",
            "3. 优化PCB布局，减少寄生参数",
            "4. 使用阻尼电路抑制振铃",
            "5. 选择低ESR输出电容减少纹波噪音",
            "6. 机械固定变压器减少振动传播"
        ]
        
        for measure in measures:
            print(f"  {measure}")
        
        return {
            'optimal_frequency': optimal_freq,
            'best_core_material': best_material,
            'emi_filter': emi_filter,
            'winding_layout': layers,
            'insulation': insulation
        }

def plot_frequency_response(emi_filter):
    """绘制EMI滤波器频率响应"""
    frequencies = np.logspace(3, 8, 1000)  # 1kHz to 100MHz
    
    # 差模滤波器传递函数
    L_dm = emi_filter['L_dm']
    C_dm = emi_filter['C_dm']
    
    # 二阶低通滤波器
    w = 2 * np.pi * frequencies
    H_dm = 1 / (1 + 1j * w * L_dm / 50 - w**2 * L_dm * C_dm)  # 假设50Ω阻抗
    
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.semilogx(frequencies/1000, 20*np.log10(np.abs(H_dm)))
    plt.grid(True)
    plt.xlabel('频率 (kHz)')
    plt.ylabel('幅度 (dB)')
    plt.title('EMI滤波器频率响应 - 幅度')
    plt.axvline(x=emi_filter['f_cutoff_dm']/1000, color='r', linestyle='--', label='截止频率')
    plt.legend()
    
    plt.subplot(2, 1, 2)
    plt.semilogx(frequencies/1000, np.angle(H_dm)*180/np.pi)
    plt.grid(True)
    plt.xlabel('频率 (kHz)')
    plt.ylabel('相位 (度)')
    plt.title('EMI滤波器频率响应 - 相位')
    
    plt.tight_layout()
    plt.savefig('emi_filter_response.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 假设已有变压器设计结果
    dummy_results = {
        'np': 45,
        'ns_9v': 8,
        'ns_18v': 15
    }
    
    analyzer = NoiseAnalyzer(dummy_results)
    optimization_report = analyzer.generate_noise_optimization_report()
    
    # 绘制EMI滤波器响应
    plot_frequency_response(optimization_report['emi_filter'])
