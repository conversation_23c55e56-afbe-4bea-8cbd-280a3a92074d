#!/usr/bin/env python3
"""
反激变压器设计计算器
设计要求：
- 输入：12V DC
- 输出：4路9V@4.5W + 2路18V@4.5W
- 总功率：27W
- 目标：最低噪音设计
"""

import math
import numpy as np
import matplotlib.pyplot as plt
from dataclasses import dataclass
from typing import List, Tuple

@dataclass
class TransformerSpec:
    """变压器规格定义"""
    # 输入参数
    vin_min: float = 10.8  # 最小输入电压 (12V - 10%)
    vin_max: float = 13.2  # 最大输入电压 (12V + 10%)
    vin_nom: float = 12.0  # 标称输入电压
    
    # 输出参数
    vout_9v: float = 9.0   # 9V输出
    vout_18v: float = 18.0 # 18V输出
    iout_9v: float = 0.5   # 9V输出电流 (4.5W/9V)
    iout_18v: float = 0.25 # 18V输出电流 (4.5W/18V)
    num_9v: int = 4        # 9V输出路数
    num_18v: int = 2       # 18V输出路数
    
    # 设计参数
    efficiency: float = 0.85  # 预估效率
    ripple_factor: float = 0.1  # 纹波系数
    frequency: float = 100000   # 开关频率 100kHz (低噪音考虑)

class FlybackTransformerDesigner:
    """反激变压器设计器"""
    
    def __init__(self, spec: TransformerSpec):
        self.spec = spec
        self.results = {}
        
    def calculate_power_requirements(self):
        """计算功率需求"""
        # 输出功率
        p_out_9v = self.spec.vout_9v * self.spec.iout_9v * self.spec.num_9v
        p_out_18v = self.spec.vout_18v * self.spec.iout_18v * self.spec.num_18v
        p_out_total = p_out_9v + p_out_18v
        
        # 输入功率
        p_in = p_out_total / self.spec.efficiency
        i_in_avg = p_in / self.spec.vin_nom
        
        self.results.update({
            'p_out_9v': p_out_9v,
            'p_out_18v': p_out_18v,
            'p_out_total': p_out_total,
            'p_in': p_in,
            'i_in_avg': i_in_avg
        })
        
        print(f"功率计算结果:")
        print(f"  9V输出功率: {p_out_9v:.1f}W")
        print(f"  18V输出功率: {p_out_18v:.1f}W")
        print(f"  总输出功率: {p_out_total:.1f}W")
        print(f"  输入功率: {p_in:.1f}W")
        print(f"  平均输入电流: {i_in_avg:.2f}A")
        
    def calculate_duty_cycle(self):
        """计算占空比"""
        # 反激变压器的占空比计算
        # 考虑二极管压降和安全裕量
        vf_diode = 0.7  # 二极管压降
        
        # 对于9V输出的占空比
        n_9v = (self.spec.vout_9v + vf_diode) / self.spec.vin_min
        d_max_9v = n_9v / (1 + n_9v)
        
        # 对于18V输出的占空比  
        n_18v = (self.spec.vout_18v + vf_diode) / self.spec.vin_min
        d_max_18v = n_18v / (1 + n_18v)
        
        # 选择较小的占空比以满足所有输出
        d_max = min(d_max_9v, d_max_18v)
        d_design = d_max * 0.8  # 80%的最大占空比作为设计值
        
        self.results.update({
            'n_9v': n_9v,
            'n_18v': n_18v,
            'd_max_9v': d_max_9v,
            'd_max_18v': d_max_18v,
            'd_max': d_max,
            'd_design': d_design
        })
        
        print(f"\n占空比计算结果:")
        print(f"  9V输出匝比: {n_9v:.3f}")
        print(f"  18V输出匝比: {n_18v:.3f}")
        print(f"  最大占空比: {d_max:.3f}")
        print(f"  设计占空比: {d_design:.3f}")
        
    def calculate_magnetizing_inductance(self):
        """计算激磁电感"""
        d = self.results['d_design']
        f = self.spec.frequency
        p_in = self.results['p_in']
        vin_min = self.spec.vin_min
        
        # 激磁电感计算 (连续导通模式)
        # Lm = (Vin_min^2 * D^2) / (2 * Pin * f)
        lm = (vin_min**2 * d**2) / (2 * p_in * f)
        
        # 峰值激磁电流
        i_lm_peak = (vin_min * d) / (lm * f)
        
        # RMS激磁电流
        i_lm_rms = i_lm_peak * d / math.sqrt(3)
        
        self.results.update({
            'lm': lm,
            'i_lm_peak': i_lm_peak,
            'i_lm_rms': i_lm_rms
        })
        
        print(f"\n激磁电感计算结果:")
        print(f"  激磁电感: {lm*1e6:.1f}μH")
        print(f"  峰值激磁电流: {i_lm_peak:.2f}A")
        print(f"  RMS激磁电流: {i_lm_rms:.2f}A")
        
    def calculate_turns_ratio_and_turns(self):
        """计算匝数比和匝数"""
        # 选择合适的磁芯
        # 对于27W功率，选择EE25或EE28磁芯
        core_type = "EE25"
        ae = 0.53e-4  # EE25有效截面积 (m²)
        le = 5.8e-2   # EE25有效磁路长度 (m)
        al = 2500e-9  # EE25电感系数 (H/turn²)
        
        # 根据激磁电感计算一次侧匝数
        lm = self.results['lm']
        np = math.sqrt(lm / al)
        np = round(np)  # 圆整到整数
        
        # 重新计算实际激磁电感
        lm_actual = al * np**2
        
        # 计算各输出的二次侧匝数
        n_9v = self.results['n_9v']
        n_18v = self.results['n_18v']
        
        ns_9v = round(np / n_9v)
        ns_18v = round(np / n_18v)
        
        # 重新计算实际匝数比
        n_actual_9v = np / ns_9v
        n_actual_18v = np / ns_18v
        
        self.results.update({
            'core_type': core_type,
            'ae': ae,
            'le': le,
            'al': al,
            'np': np,
            'ns_9v': ns_9v,
            'ns_18v': ns_18v,
            'lm_actual': lm_actual,
            'n_actual_9v': n_actual_9v,
            'n_actual_18v': n_actual_18v
        })
        
        print(f"\n匝数计算结果:")
        print(f"  磁芯型号: {core_type}")
        print(f"  一次侧匝数: {np}")
        print(f"  9V二次侧匝数: {ns_9v}")
        print(f"  18V二次侧匝数: {ns_18v}")
        print(f"  实际激磁电感: {lm_actual*1e6:.1f}μH")
        print(f"  实际9V匝数比: {n_actual_9v:.3f}")
        print(f"  实际18V匝数比: {n_actual_18v:.3f}")
        
    def calculate_wire_gauge(self):
        """计算导线规格"""
        # 电流密度选择 (A/mm²)
        j = 4.0  # 4A/mm² (低噪音设计，选择较低电流密度)
        
        # 一次侧导线
        i_p_rms = self.results['i_lm_rms']
        area_p = i_p_rms / j
        awg_p = self.area_to_awg(area_p)
        
        # 9V二次侧导线
        i_s_9v = self.spec.iout_9v
        area_s_9v = i_s_9v / j
        awg_s_9v = self.area_to_awg(area_s_9v)
        
        # 18V二次侧导线
        i_s_18v = self.spec.iout_18v
        area_s_18v = i_s_18v / j
        awg_s_18v = self.area_to_awg(area_s_18v)
        
        self.results.update({
            'j': j,
            'area_p': area_p,
            'awg_p': awg_p,
            'area_s_9v': area_s_9v,
            'awg_s_9v': awg_s_9v,
            'area_s_18v': area_s_18v,
            'awg_s_18v': awg_s_18v
        })
        
        print(f"\n导线规格计算:")
        print(f"  电流密度: {j}A/mm²")
        print(f"  一次侧: AWG{awg_p} ({area_p:.2f}mm²)")
        print(f"  9V二次侧: AWG{awg_s_9v} ({area_s_9v:.2f}mm²)")
        print(f"  18V二次侧: AWG{awg_s_18v} ({area_s_18v:.2f}mm²)")
        
    def area_to_awg(self, area_mm2):
        """将截面积转换为AWG线规"""
        # AWG对照表 (近似)
        awg_table = {
            0.05: 30, 0.08: 28, 0.13: 26, 0.20: 24, 0.32: 22,
            0.52: 20, 0.82: 18, 1.31: 16, 2.08: 14, 3.31: 12,
            5.26: 10, 8.37: 8, 13.3: 6, 21.1: 4, 33.6: 2
        }
        
        for area, awg in sorted(awg_table.items()):
            if area_mm2 <= area:
                return awg
        return 0  # 如果需要更粗的线
        
    def design_complete_transformer(self):
        """完整的变压器设计流程"""
        print("=" * 50)
        print("反激变压器设计计算")
        print("=" * 50)
        
        self.calculate_power_requirements()
        self.calculate_duty_cycle()
        self.calculate_magnetizing_inductance()
        self.calculate_turns_ratio_and_turns()
        self.calculate_wire_gauge()
        
        return self.results

if __name__ == "__main__":
    # 创建设计规格
    spec = TransformerSpec()
    
    # 创建设计器并执行设计
    designer = FlybackTransformerDesigner(spec)
    results = designer.design_complete_transformer()
    
    print("\n" + "=" * 50)
    print("设计总结")
    print("=" * 50)
    print(f"磁芯: {results['core_type']}")
    print(f"一次侧: {results['np']}匝, AWG{results['awg_p']}")
    print(f"9V二次侧: {results['ns_9v']}匝 × 4路, AWG{results['awg_s_9v']}")
    print(f"18V二次侧: {results['ns_18v']}匝 × 2路, AWG{results['awg_s_18v']}")
    print(f"激磁电感: {results['lm_actual']*1e6:.1f}μH")
    print(f"开关频率: {spec.frequency/1000:.0f}kHz")
