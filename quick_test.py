#!/usr/bin/env python3
"""
快速测试反激变压器设计
不依赖外部库，直接计算核心参数
"""

import math

def quick_design_test():
    """快速设计测试"""
    print("=" * 60)
    print("反激变压器快速设计测试")
    print("=" * 60)
    
    # 设计规格
    vin_nom = 12.0      # 输入电压
    vin_min = 10.8      # 最小输入电压
    vout_9v = 9.0       # 9V输出
    vout_18v = 18.0     # 18V输出
    iout_9v = 0.5       # 9V输出电流
    iout_18v = 0.25     # 18V输出电流
    num_9v = 4          # 9V路数
    num_18v = 2         # 18V路数
    efficiency = 0.85   # 效率
    frequency = 100000  # 开关频率 100kHz
    
    print(f"设计规格:")
    print(f"  输入电压: {vin_nom}V")
    print(f"  输出1: {num_9v}路 × {vout_9v}V @ {iout_9v}A")
    print(f"  输出2: {num_18v}路 × {vout_18v}V @ {iout_18v}A")
    print(f"  开关频率: {frequency/1000}kHz")
    
    # 功率计算
    p_out_9v = vout_9v * iout_9v * num_9v
    p_out_18v = vout_18v * iout_18v * num_18v
    p_out_total = p_out_9v + p_out_18v
    p_in = p_out_total / efficiency
    
    print(f"\n功率分析:")
    print(f"  9V输出功率: {p_out_9v}W")
    print(f"  18V输出功率: {p_out_18v}W")
    print(f"  总输出功率: {p_out_total}W")
    print(f"  输入功率: {p_in:.1f}W")
    
    # 占空比计算
    vf_diode = 0.7
    n_9v = (vout_9v + vf_diode) / vin_min
    n_18v = (vout_18v + vf_diode) / vin_min
    d_max_9v = n_9v / (1 + n_9v)
    d_max_18v = n_18v / (1 + n_18v)
    d_max = min(d_max_9v, d_max_18v)
    d_design = d_max * 0.8
    
    print(f"\n占空比分析:")
    print(f"  9V匝数比: {n_9v:.3f}")
    print(f"  18V匝数比: {n_18v:.3f}")
    print(f"  最大占空比: {d_max:.3f}")
    print(f"  设计占空比: {d_design:.3f}")
    
    # 激磁电感计算
    lm = (vin_min**2 * d_design**2) / (2 * p_in * frequency)
    i_lm_peak = (vin_min * d_design) / (lm * frequency)
    i_lm_rms = i_lm_peak * d_design / math.sqrt(3)
    
    print(f"\n激磁电感设计:")
    print(f"  激磁电感: {lm*1e6:.1f}μH")
    print(f"  峰值电流: {i_lm_peak:.2f}A")
    print(f"  RMS电流: {i_lm_rms:.2f}A")
    
    # 匝数计算 (假设EE25磁芯)
    al = 2500e-9  # EE25电感系数
    np = math.sqrt(lm / al)
    np = round(np)
    lm_actual = al * np**2
    
    ns_9v = round(np / n_9v)
    ns_18v = round(np / n_18v)
    
    print(f"\n匝数设计:")
    print(f"  磁芯: EE25")
    print(f"  一次侧匝数: {np}")
    print(f"  9V二次侧匝数: {ns_9v}")
    print(f"  18V二次侧匝数: {ns_18v}")
    print(f"  实际激磁电感: {lm_actual*1e6:.1f}μH")
    
    # 导线规格计算
    j = 4.0  # 电流密度 A/mm²
    
    # AWG对照表
    awg_areas = {
        30: 0.05, 28: 0.08, 26: 0.13, 24: 0.20, 22: 0.32,
        20: 0.52, 18: 0.82, 16: 1.31, 14: 2.08, 12: 3.31
    }
    
    def get_awg(current):
        area_needed = current / j
        for awg, area in sorted(awg_areas.items(), reverse=True):
            if area_needed <= area:
                return awg
        return 12  # 最粗的线
    
    awg_p = get_awg(i_lm_rms)
    awg_s_9v = get_awg(iout_9v)
    awg_s_18v = get_awg(iout_18v)
    
    print(f"\n导线规格:")
    print(f"  一次侧: AWG{awg_p}")
    print(f"  9V二次侧: AWG{awg_s_9v}")
    print(f"  18V二次侧: AWG{awg_s_18v}")
    
    # 噪音优化建议
    print(f"\n噪音优化建议:")
    print(f"  开关频率: {frequency/1000}kHz (避开可听频段)")
    print(f"  磁芯材料: N27 (低损耗、低磁致伸缩)")
    print(f"  绕组布局: 三明治结构")
    print(f"  EMI滤波: 差模100μH+0.1μF, 共模1mH+2.2nF")
    
    # 性能预估
    print(f"\n性能预估:")
    print(f"  预估效率: {efficiency*100}%")
    print(f"  预估纹波: 9V<100mV, 18V<200mV")
    print(f"  预估温升: <80°C")
    
    # 制造指导
    print(f"\n制造指导:")
    print(f"  1. 使用EE25磁芯和专用骨架")
    print(f"  2. 绕制顺序: P1({np//2}T) → S9V({ns_9v}T×4) → P2({np-np//2}T) → S18V({ns_18v}T×2)")
    print(f"  3. 层间绝缘: 0.1mm绝缘纸")
    print(f"  4. 安全绝缘: 一次-二次间3mm")
    print(f"  5. 最终浸漆处理")
    
    print(f"\n" + "=" * 60)
    print("设计完成！建议制作样品进行实际验证。")
    print("=" * 60)
    
    return {
        'core_type': 'EE25',
        'np': np,
        'ns_9v': ns_9v,
        'ns_18v': ns_18v,
        'lm_actual': lm_actual,
        'd_design': d_design,
        'awg_p': awg_p,
        'awg_s_9v': awg_s_9v,
        'awg_s_18v': awg_s_18v,
        'frequency': frequency,
        'efficiency': efficiency
    }

if __name__ == "__main__":
    results = quick_design_test()
    
    # 保存结果到文件
    try:
        with open("design_results.txt", "w", encoding="utf-8") as f:
            f.write("反激变压器设计结果\n")
            f.write("=" * 30 + "\n")
            for key, value in results.items():
                f.write(f"{key}: {value}\n")
        print("\n设计结果已保存到 design_results.txt")
    except Exception as e:
        print(f"保存文件时出错: {e}")
    
    input("\n按回车键退出...")
